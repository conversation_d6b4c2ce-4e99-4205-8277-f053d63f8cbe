{"project_id": "gcp", "benchmark": "cis_v300", "timestamp": "20250724_163029", "results": {"benchmark_results": {"group_id": "root_result_group", "title": "CIS v3.0.0", "description": "", "tags": {}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 84}}, "groups": [{"group_id": "gcp_compliance.benchmark.cis_v300", "title": "CIS v3.0.0", "description": "The CIS Google Cloud Platform Foundations Security Benchmark covers foundational elements of Google Cloud Platform.", "tags": {"benchmark": "cis", "category": "Compliance", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 84}}, "groups": [{"group_id": "gcp_compliance.benchmark.cis_v300_1", "title": "1 Identity and Access Management", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "1", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 17}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_1", "description": "Use corporate login credentials instead of personal accounts, such as Gmail accounts.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.1", "cis_level": "1", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.1 Ensure that Corporate Login Credentials are Used", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_2", "description": "Setup multi-factor authentication for Google Cloud Platform accounts.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.2", "cis_level": "1", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.2 Ensure that Multi-Factor Authentication is 'Enabled' for All Non-Service Accounts", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_3", "description": "Setup Security Key Enforcement for Google Cloud Platform admin accounts.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.3", "cis_level": "2", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.3 Ensure that Security Key Enforcement is Enabled for All Admin Accounts", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_4", "description": "User managed service accounts should not have user-managed keys.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.4", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.4 Ensure That There Are Only GCP-Managed Service Account Keys for Each Service Account", "run_status": 8, "run_error": "relation \"gcp_service_account_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_5", "description": "A service account is a special Google account that belongs to an application or a VM, instead of to an individual end-user. The application uses the service account to call the service's Google API so that users aren't directly involved. It's recommended not to use admin access for ServiceAccount.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.5", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.5 Ensure That Service Account Has No Admin Privileges", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_6", "description": "It is recommended to assign the Service Account User (iam.serviceAccountUser) and Service Account Token Creator (iam.serviceAccountTokenCreator) roles to a user for a specific service account rather than assigning the role to a user at project level.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.6", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.6 Ensure That IAM Users Are Not Assigned the Service Account User or Service Account Token Creator Roles at Project Level", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_7", "description": "Service Account keys consist of a key ID (Private_key_Id) and Private key, which are used to sign programmatic requests users make to Google cloud services accessible to that particular service account. It is recommended that all Service Account keys are regularly rotated.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.7", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.7 Ensure User-Managed/External Keys for Service Accounts Are Rotated Every 90 Days or Fewer", "run_status": 8, "run_error": "relation \"gcp_service_account_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_8", "description": "It is recommended that the principle of 'Separation of Duties' is enforced while assigning service-account related roles to users.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.8", "cis_level": "2", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.8 Ensure That Separation of Duties Is Enforced While Assigning Service Account Related Roles to Users", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_9", "description": "It is recommended that the IAM policy on Cloud KMS cryptokeys should restrict anonymous and/or public access.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.9", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/KMS"}, "title": "1.9 Ensure That Cloud KMS Cryptokeys Are Not Anonymously or Publicly Accessible", "run_status": 8, "run_error": "relation \"gcp_kms_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_10", "description": "Google Cloud Key Management Service stores cryptographic keys in a hierarchical structure designed for useful and elegant access control management. The format for the rotation schedule depends on the client library that is used. For the gcloud command-line tool, the next rotation time must be in ISO or RFC3339 format, and the rotation period must be in the form INTEGER[UNIT], where units can be one of seconds (s), minutes (m), hours (h) or days (d).", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.10", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/KMS"}, "title": "1.10 Ensure KMS Encryption Keys Are Rotated Within a Period of 90 Days", "run_status": 8, "run_error": "relation \"gcp_kms_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_11", "description": "It is recommended that the principle of 'Separation of Duties' is enforced while assigning KMS related roles to users.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.11", "cis_level": "2", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/KMS"}, "title": "1.11 Ensure That Separation of Duties Is Enforced While Assigning KMS Related Roles to Users", "run_status": 8, "run_error": "relation \"gcp_iam_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_12", "description": "API Keys should only be used for services in cases where other authentication methods are unavailable. Unused keys with their permissions in tact may still exist within a project. Keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to use standard authentication flow instead.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.12", "cis_level": "2", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.12 Ensure API Keys Only Exist for Active Services", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_13", "description": "API Keys should only be used for services in cases where other authentication methods are unavailable. In this case, unrestricted keys are insecure because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API key usage to trusted hosts, HTTP referrers and apps. It is recommended to use the more secure standard authentication flow instead.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.13", "cis_level": "2", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.13 Ensure API Keys Are Restricted To Use by Only Specified Hosts and Apps", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_14", "description": "API Keys should only be used for services in cases where other authentication methods are unavailable. API keys are always at risk because they can be viewed publicly, such as from within a browser, or they can be accessed on a device where the key resides. It is recommended to restrict API keys to use (call) only APIs required by an application.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.14", "cis_level": "2", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.14 Ensure API Keys Are Restricted to Only APIs That Application Needs Access", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_15", "description": "API Keys should only be used for services in cases where other authentication methods are unavailable. If they are in use it is recommended to rotate API keys every 90 days.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.15", "cis_level": "2", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/IAM"}, "title": "1.15 Ensure API Keys Are Rotated Every 90 Days", "run_status": 8, "run_error": "relation \"gcp_apikeys_key\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_16", "description": "It is recommended that Essential Contacts is configured to designate email addresses for Google Cloud services to notify of important technical or security information.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.16", "cis_level": "1", "cis_section_id": "1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Organization"}, "title": "1.16 Ensure Essential Contacts is Configured for Organization", "run_status": 8, "run_error": "relation \"gcp_organization\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_1_17", "description": "Google Cloud Functions allow you to host serverless code that is executed when an event is triggered, without the requiring the management a host operating system. These functions can also store environment variables to be used by the code that may contain authentication or other information that needs to remain confidential.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "1.17", "cis_level": "1", "cis_section_id": "1", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Dataproc"}, "title": "1.17 Ensure Secrets are Not Stored in Cloud Functions Environment Variables by Using Secret Manager", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v300_2", "title": "2 Logging and Monitoring", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "2", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 16}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_1", "description": "It is recommended that Cloud Audit Logging is configured to track all admin activities and read, write access to user data.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.1", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.1 Ensure That Cloud Audit Logging Is Configured Properly ", "run_status": 8, "run_error": "relation \"gcp_audit_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_2", "description": "It is recommended to create a sink that will export copies of all the log entries. This can help aggregate logs from multiple projects and export them to a Security Information and Event Management (SIEM).", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.2", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.2 Ensure That Sinks Are Configured for All Log Entries", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_3", "description": "Enabling retention policies on log buckets will protect logs stored in cloud storage buckets from being overwritten or accidentally deleted. It is recommended to set up retention policies and configure Bucket Lock on all storage buckets that are used as log sinks.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.3", "cis_level": "2", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.3 Ensure That Retention Policies on Cloud Storage Buckets Used for Exporting Logs Are Configured Using Bucket Lock", "run_status": 8, "run_error": "relation \"gcp_logging_sink\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_4", "description": "In order to prevent unnecessary project ownership assignments to users/service-accounts and further misuses of projects and resources, all roles/Owner assignments should be monitored. Members (users/Service-Accounts) with a role assignment to primitive role roles/Owner are project owners.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.4", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.4 Ensure Log Metric Filter and <PERSON><PERSON>s Exist for Project Ownership Assignments/Changes ", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_5", "description": "Google Cloud Platform (GCP) services write audit log entries to the Admin Activity and Data Access logs to help answer the questions of, 'who did what, where, and when?' within GCP projects.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.5", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.5 Ensure That the Log Metric Filter and Alerts Exist for Audit Configuration Changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_6", "description": "It is recommended that a metric filter and alarm be established for changes to Identity and Access Management (IAM) role creation, deletion and updating activities.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.6", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.6 Ensure That the Log Metric Filter and Alerts Exist for Custom Role Changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_7", "description": "It is recommended that a metric filter and alarm be established for Virtual Private Cloud (VPC) Network Firewall rule changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.7", "cis_level": "2", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.7 Ensure That the Log Metric Filter and Alerts Exist for VPC Network Firewall Rule Changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_8", "description": "It is recommended that a metric filter and alarm be established for Virtual Private Cloud (VPC) network route changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.8", "cis_level": "2", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.8 Ensure That the Log Metric Filter and Alerts Exist for VPC Network Route Changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_9", "description": "It is recommended that a metric filter and alarm be established for Virtual Private Cloud (VPC) network changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.9", "cis_level": "2", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.9 Ensure That the Log Metric Filter and Alerts Exist for VPC Network Changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_10", "description": "It is recommended that a metric filter and alarm be established for Cloud Storage Bucket IAM changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.10", "cis_level": "2", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.10 Ensure That the Log Metric Filter and Alerts Exist for Cloud Storage IAM Permission Changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_11", "description": "It is recommended that a metric filter and alarm be established for SQL instance configuration changes.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.11", "cis_level": "2", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.11 Ensure That the Log Metric Filter and Alerts Exist for SQL Instance Configuration Changes", "run_status": 8, "run_error": "relation \"gcp_monitoring_alert_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_12", "description": "Cloud DNS logging records the queries from the name servers within your VPC to Stackdriver. Logged queries can come from Compute Engine VMs, GKE containers, or other GCP resources provisioned within the VPC.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.12", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/DNS"}, "title": "2.12 Ensure That Cloud DNS Logging Is Enabled for All VPC Networks", "run_status": 8, "run_error": "relation \"gcp_dns_policy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_13", "description": "GCP Cloud Asset Inventory is services that provides a historical view of GCP resources and IAM policies through a time-series database. The information recorded includes metadata on Google Cloud resources, metadata on policies set on Google Cloud projects or resources, and runtime information gathered within a Google Cloud resource.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.13", "cis_level": "1", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Project"}, "title": "2.13 Ensure Cloud Asset Inventory Is Enabled", "run_status": 8, "run_error": "relation \"gcp_project_service\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_14", "description": "GCP Access Transparency provides audit logs for all actions that Google personnel take in your Google Cloud resources.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.14", "cis_level": "2", "cis_section_id": "2", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Project"}, "title": "2.14 Ensure 'Access Transparency' is 'Enabled'", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_15", "description": "GCP Access Approval enables you to require your organizations' explicit approval whenever Google support try to access your projects. You can then select users within your organization who can approve these requests through giving them a security role in IAM. All access requests display which Google Employee requested them in an email or Pub/Sub message that you can choose to Approve. This adds an additional control and logging of who in your organization approved/denied these requests.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.15", "cis_level": "2", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Project"}, "title": "2.15 Ensure 'Access Approval' is 'Enabled'", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_2_16", "description": "Logging enabled on a HTTPS Load Balancer will show all network traffic and its destination.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "2.16", "cis_level": "2", "cis_section_id": "2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Logging"}, "title": "2.16 Ensure Logging is enabled for HTTP(S) Load Balancer", "run_status": 8, "run_error": "relation \"gcp_compute_url_map\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v300_3", "title": "3 Networking", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "3", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 10}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_1", "description": "To prevent use of default network, a project should not have a default network.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.1", "cis_level": "2", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.1 Ensure That the Default Network Does Not Exist in a Project", "run_status": 8, "run_error": "relation \"gcp_compute_network\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_2", "description": "In order to prevent use of legacy networks, a project should not have a legacy network configured.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.2", "cis_level": "1", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.2 Ensure Legacy Networks Do Not Exist for Older Projects", "run_status": 8, "run_error": "relation \"gcp_compute_network\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_3", "description": "Cloud Domain Name System (DNS) is a fast, reliable and cost-effective domain name system that powers millions of domains on the internet. Domain Name System Security Extensions (DNSSEC) in Cloud DNS enables domain owners to take easy steps to protect their domains against DNS hijacking and man-in-the-middle and other attacks.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.3", "cis_level": "1", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/DNS"}, "title": "3.3 Ensure That DNSSEC Is Enabled for Cloud DNS", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_4", "description": "DNSSEC algorithm numbers in this registry may be used in CERT RRs. Zone signing (DNSSEC) and transaction security mechanisms (SIG(0) and TSIG) make use of particular subsets of these algorithms. The algorithm used for key signing should be a recommended one and it should be strong.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.4", "cis_level": "1", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/DNS"}, "title": "3.4 Ensure That RSASHA1 Is Not Used for the Key-Signing Key in Cloud DNS DNSSEC", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_5", "description": "DNSSEC algorithm numbers in this registry may be used in CERT RRs. Zone signing (DNSSEC) and transaction security mechanisms (SIG(0) and TSIG) make use of particular subsets of these algorithms. The algorithm used for key signing should be a recommended one and it should be strong.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.5", "cis_level": "1", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/DNS"}, "title": "3.5 Ensure That RSASHA1 Is Not Used for the Zone-Signing Key in Cloud DNS DNSSEC", "run_status": 8, "run_error": "relation \"gcp_dns_managed_zone\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_6", "description": "GCP Firewall Rules are specific to a VPC Network. Each rule either allows or denies traffic when its conditions are met. Its conditions allow the user to specify the type of traffic, such as ports and protocols, and the source or destination of the traffic, including IP addresses, subnets, and instances.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.6", "cis_level": "2", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.6 Ensure That SSH Access Is Restricted From the Internet", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_7", "description": "GCP Firewall Rules are specific to a VPC Network. Each rule either allows or denies traffic when its conditions are met. Its conditions allow users to specify the type of traffic, such as ports and protocols, and the source or destination of the traffic, including IP addresses, subnets, and instances.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.7", "cis_level": "2", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.7 Ensure That RDP Access Is Restricted From the Internet ", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_8", "description": "Flow Logs is a feature that enables users to capture information about the IP traffic going to and from network interfaces in the organization's VPC Subnets. Once a flow log is created, the user can view and retrieve its data in Stackdriver Logging. It is recommended that Flow Logs be enabled for every business-critical VPC subnet.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.8", "cis_level": "2", "cis_section_id": "3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.8 Ensure that VPC Flow Logs is Enabled for Every Subnet in a VPC Network", "run_status": 8, "run_error": "relation \"gcp_compute_subnetwork\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_9", "description": "Secure Sockets Layer (SSL) policies determine what port Transport Layer Security (TLS) features clients are permitted to use when connecting to load balancers.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.9", "cis_level": "1", "cis_section_id": "3", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.9 Ensure No HTTPS or SSL Proxy Load Balancers Permit SSL Policies With Weak Cipher Suites", "run_status": 8, "run_error": "relation \"gcp_compute_target_ssl_proxy\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_3_10", "description": "IAP authenticates the user requests to your apps via a Google single sign in. You can then manage these users with permissions to control access. It is recommended to use both IAP permissions and firewalls to restrict this access to your apps with sensitive information.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "3.10", "cis_level": "2", "cis_section_id": "3", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "3.10 Use Identity Aware Proxy (IAP) to Ensure Only Traffic From Google IP Addresses are 'Allowed'", "run_status": 8, "run_error": "relation \"gcp_compute_firewall\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v300_4", "title": "4 Virtual Machines", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "4", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 12}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_1", "description": "It is recommended to configure your instance to not use the default Compute Engine service account because it has the Editor role on the project.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.1", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.1 Ensure That Instances Are Not Configured To Use the Default Service Account", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_2", "description": "To support principle of least privileges and prevent potential privilege escalation it is recommended that instances are not assigned to default service account Compute Engine default service account with Scope Allow full access to all Cloud APIs.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.2", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.2 Ensure That Instances Are Not Configured To Use the Default Service Account With Full Access to All Cloud APIs", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_3", "description": "It is recommended to use Instance specific SSH key(s) instead of using common/shared project-wide SSH key(s) to access Instances.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.3", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.3 Ensure 'Block Project-Wide SSH Keys' Is Enabled for VM Instances", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_4", "description": "Enabling OS login binds SSH certificates to IAM users and facilitates effective SSH certificate management.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.4", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.4 Ensure Oslogin Is Enabled for a Project", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_5", "description": "Interacting with a serial port is often referred to as the serial console, which is similar to using a terminal window, in that input and output is entirely in text mode and there is no graphical interface or mouse support.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.5", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.5 Ensure ‘Enable Connecting to Serial Ports’ Is Not Enabled for VM Instance ", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_6", "description": "Compute Engine instance cannot forward a packet unless the source IP address of the packet matches the IP address of the instance. Similarly, GCP won't deliver a packet whose destination IP address is different than the IP address of the instance receiving the packet. However, both capabilities are required if you want to use instances to help route packets.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.6", "cis_level": "1", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.6 Ensure That IP Forwarding Is Not Enabled on Instances", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_7", "description": "Customer-Supplied Encryption Keys (CSEK) are a feature in Google Cloud Storage and Google Compute Engine. If you supply your own encryption keys, Google uses your key to protect the Google-generated keys used to encrypt and decrypt your data. By default, Google Compute Engine encrypts all data at rest. Compute Engine handles and manages this encryption for you without any additional actions on your part. However, if you wanted to control and manage this encryption yourself, you can provide your own encryption keys.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.7", "cis_level": "2", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.7 Ensure VM Disks for Critical VMs Are Encrypted With Customer-Supplied Encryption Keys", "run_status": 8, "run_error": "relation \"gcp_compute_disk\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_8", "description": "To defend against against advanced threats and ensure that the boot loader and firmware on your VMs are signed and untampered, it is recommended that Compute instances are launched with Shielded VM enabled.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.8", "cis_level": "2", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.8 Ensure Compute Instances Are Launched With Shielded VM Enabled", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_9", "description": "Compute instances should not be configured to have external IP addresses.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.9", "cis_level": "2", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.9 Ensure That Compute Instances Do Not Have Public IP Addresses", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_10", "description": "In order to maintain the highest level of security all connections to an application should be secure by default.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.10", "cis_level": "2", "cis_section_id": "4", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/AppEngine"}, "title": "4.10 Ensure That App Engine Applications Enforce HTTPS Connections", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_11", "description": "Google Cloud encrypts data at-rest and in-transit, but customer data must be decrypted for processing. Confidential Computing is a breakthrough technology which encrypts data in-use—while it is being processed. Confidential Computing environments keep data encrypted in memory and elsewhere outside the central processing unit (CPU).", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.11", "cis_level": "2", "cis_section_id": "4", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.11 Ensure That Compute Instances Have Confidential Computing Enabled", "run_status": 8, "run_error": "relation \"gcp_compute_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_4_12", "description": "Google Cloud Virtual Machines have the ability via an OS Config agent API to periodically (about every 10 minutes) report OS inventory data. A patch compliance API periodically reads this data, and cross references metadata to determine if the latest updates are installed.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "4.12", "cis_level": "2", "cis_section_id": "4", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Compute"}, "title": "4.12 Ensure the Latest Operating System Updates Are Installed On Your Virtual Machines in All Project", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v300_5", "title": "5 Storage", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "5", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Storage", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 2}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_5_1", "description": "It is recommended that IAM policy on Cloud Storage bucket does not allows anonymous or public access.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "5.1", "cis_level": "1", "cis_section_id": "5", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Storage"}, "title": "5.1 Ensure That Cloud Storage Bucket Is Not Anonymously or Publicly Accessible", "run_status": 8, "run_error": "relation \"gcp_storage_bucket\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_5_2", "description": "It is recommended that uniform bucket-level access is enabled on Cloud Storage buckets.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "5.2", "cis_level": "2", "cis_section_id": "5", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Storage"}, "title": "5.2 Ensure That Cloud Storage Buckets Have Uniform BucketLevel Access Enabled", "run_status": 8, "run_error": "relation \"gcp_storage_bucket\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v300_6", "title": "6 Cloud SQL Database Services", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "6", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 22}}, "groups": [{"group_id": "gcp_compliance.benchmark.cis_v300_6_1", "title": "6.1 MySQL Database", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "6.1", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 3}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_1_1", "description": "It is recommended to set a password for the administrative user (root by default) to prevent unauthorized access to the SQL database instances. This recommendation is applicable only for MySQL Instances. PostgreSQL does not offer any setting for No Password from the cloud console.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.1.1", "cis_level": "1", "cis_section_id": "6.1", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.1.1 Ensure That a MySQL Database Instance Does Not Allow Anyone To Connect With Administrative Privileges", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_1_2", "description": "It is recommended to set skip_show_database database flag for Cloud SQL Mysql instance to on.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.1.2", "cis_level": "1", "cis_section_id": "6.1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.1.2 Ensure 'Skip_show_database' Database Flag for Cloud SQL MySQL Instance Is Set to 'On'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_1_3", "description": "It is recommended to set the local_infile database flag for a Cloud SQL MySQL instance to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.1.3", "cis_level": "1", "cis_section_id": "6.1", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.1.3 Ensure That the 'Local_infile' Database Flag for a Cloud SQL MySQL Instance Is Set to 'Off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v300_6_2", "title": "6.2 PostgreSQL Database", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "6.2", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 8}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_2_1", "description": "The log_error_verbosity flag controls the verbosity/details of messages logged. Valid values are: 'TERSE', 'DEFAULT', and 'VERBOSE'.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.1", "cis_level": "2", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.1 Ensure 'Log_error_verbosity' Database Flag for Cloud SQL PostgreSQL Instance Is Set to 'DEFAULT' or Stricter", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_2_2", "description": "Enabling the log_connections setting causes each attempted connection to the server to be logged, along with successful completion of client authentication. This parameter cannot be changed after the session starts.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.2", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.2 Ensure That the 'Log_connections' Database Flag for Cloud SQL PostgreSQL Instance Is Set to 'On'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_2_3", "description": "Enabling the log_disconnections setting logs the end of each session, including the session duration.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.3", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.3 Ensure That the 'Log_disconnections' Database Flag for Cloud SQL PostgreSQL Instance Is Set to 'On'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_2_4", "description": "The value of log_statement flag determined the SQL statements that are logged. Valid values are: 'none', 'ddl', 'mod', and 'all'.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.4", "cis_level": "2", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL"}, "title": "6.2.4 Ensure 'Log_statement' Database Flag for Cloud SQL PostgreSQL Instance Is Set Appropriately", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_2_5", "description": "The log_min_messages flag defines the minimum message severity level that is considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include DEBUG5, DEBUG4, DEBUG3, DEBUG2, DEBUG1, INFO, NOTICE, WARNING, ERROR, LOG, FATAL, and PANIC. Each severity level includes the subsequent levels mentioned above.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.5", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.2.5 Ensure that the 'Log_min_messages' Flag for a Cloud SQL PostgreSQL Instance is set at minimum to 'Warning'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_2_6", "description": "The log_min_error_statement flag defines the minimum message severity level that are considered as an error statement. Messages for error statements are logged with the SQL statement. Valid values include DEBUG5, DEBUG4, DEBUG3, DEBUG2, DEBUG1, INFO, NOTICE, WARNING, ERROR, LOG, FATAL, and PANIC. Each severity level includes the subsequent levels mentioned above. Ensure a value of ERROR or stricter is set.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.6", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.2.6 Ensure 'Log_min_error_statement' Database Flag for Cloud SQL PostgreSQL Instance Is Set to 'Error' or Stricter", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_2_7", "description": "The log_min_duration_statement flag defines the minimum amount of execution time of a statement in milliseconds where the total duration of the statement is logged. Ensure that log_min_duration_statement is disabled, i.e., a value of -1 is set.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.7", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.2.7 Ensure That the 'Log_min_duration_statement' Database Flag for Cloud SQL PostgreSQL Instance Is Set to '-1'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_2_8", "description": "Ensure cloudsql.enable_pgaudit database flag for Cloud SQL PostgreSQL instance is set to on to allow for centralized logging.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.2.8", "cis_level": "1", "cis_section_id": "6.2", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.2.8 Ensure That 'cloudsql.enable_pgaudit' Database Flag for each Cloud Sql Postgresql Instance Is Set to 'on' For Centralized Logging", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v300_6_3", "title": "6.3 SQL Server", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "6.3", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/SQL", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 7}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_3_1", "description": "It is recommended to set external scripts enabled database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.1", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.1 Ensure 'external scripts enabled' database flag for Cloud SQL SQL Server instance is set to 'off", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_3_2", "description": "It is recommended to set cross db ownership chaining database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.2", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.2 Ensure that the 'cross db ownership chaining' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_3_3", "description": "It is recommended to set user connections database flag for Cloud SQL SQL Server instance according organization-defined value.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.3", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.3 Ensure 'user Connections' Database Flag for Cloud Sql Sql Server Instance Is Set to a Non-limiting Value", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_3_4", "description": "It is recommended that, user options database flag for Cloud SQL SQL Server instance should not be configured.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.4", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.4 Ensure 'user options' database flag for Cloud SQL SQL Server instance is not configured", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_3_5", "description": "It is recommended to set remote access database flag for Cloud SQL SQL Server instance to off.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.5", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.5 Ensure 'remote access' database flag for Cloud SQL SQL Server instance is set to 'off'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_3_6", "description": "It is recommended to set 3625 (trace flag) database flag for Cloud SQL SQL Server instance to on.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.6", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.6 Ensure '3625 (trace flag)' database flag for all Cloud SQL Server instances is set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_3_7", "description": "It is recommended not to set contained database authentication database flag for Cloud SQL on the SQL Server instance to on.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.3.7", "cis_level": "1", "cis_section_id": "6.3", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.3.7 Ensure that the 'contained database authentication' database flag for Cloud SQL on the SQL Server instance is not set to 'on'", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_4", "description": "It is recommended to enforce all incoming connections to SQL database instance to use SSL.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.4", "cis_level": "1", "cis_section_id": "6", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.4 Ensure That the Cloud SQL Database Instance Requires All Incoming Connections To Use SSL", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_5", "description": "Database Server should accept connections only from trusted Network(s)/IP(s) and restrict access from public IP addresses.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.5", "cis_level": "1", "cis_section_id": "6", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.5 Ensure That Cloud SQL Database Instances Do Not Implicitly Whitelist All Public IP Addresses (", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_6", "description": "It is recommended to configure Second Generation Sql instance to use private IPs instead of public IPs.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.6", "cis_level": "2", "cis_section_id": "6", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.6 Ensure That Cloud SQL Database Instances Do Not Have Public IPs", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_6_7", "description": "It is recommended to have all SQL database instances set to enable automated backups.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "6.7", "cis_level": "1", "cis_section_id": "6", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP"}, "title": "6.7 Ensure That Cloud SQL Database Instances Are Configured With Automated Backups", "run_status": 8, "run_error": "relation \"gcp_sql_database_instance\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v300_7", "title": "7 BigQuery", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "7", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/BigQuery", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 4}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_7_1", "description": "It is recommended that the IAM policy on BigQuery datasets does not allow anonymous and/or public access.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "7.1", "cis_level": "1", "cis_section_id": "7", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/BigQuery"}, "title": "7.1 Ensure That BigQuery Datasets Are Not Anonymously or Publicly Accessible", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_7_2", "description": "BigQuery by default encrypts the data as rest by employing Envelope Encryption using Google managed cryptographic keys. The data is encrypted using the data encryption keys and data encryption keys themselves are further encrypted using key encryption keys. This is seamless and do not require any additional input from the user. However, if you want to have greater control, Customer-managed encryption keys (CMEK) can be used as encryption key management solution for BigQuery Data Sets. If CMEK is used, the CMEK is used to encrypt the data encryption keys instead of using google-managed encryption keys.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "7.2", "cis_level": "2", "cis_section_id": "7", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/BigQuery"}, "title": "7.2 Ensure That All BigQuery Tables Are Encrypted With Customer-Managed Encryption Key (CMEK)", "run_status": 8, "run_error": "relation \"gcp_bigquery_table\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_7_3", "description": "BigQuery by default encrypts the data as rest by employing Envelope Encryption using Google managed cryptographic keys. The data is encrypted using the data encryption keys and data encryption keys themselves are further encrypted using key encryption keys. This is seamless and do not require any additional input from the user. However, if you want to have greater control, Customer-managed encryption keys (CMEK) can be used as encryption key management solution for BigQuery Data Sets.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "7.3", "cis_level": "2", "cis_section_id": "7", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/BigQuery"}, "title": "7.3 Ensure That a Default Customer-Managed Encryption Key (CMEK) Is Specified for All BigQuery Data Sets", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}, {"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_7_4", "description": "BigQuery tables can contain sensitive data that for security purposes should be discovered, monitored, classified, and protected. Google Cloud's Sensitive Data Protection tools can automatically provide data classification of all BigQuery data across an organization.", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "7.4", "cis_level": "2", "cis_section_id": "7", "cis_type": "manual", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/BigQuery"}, "title": "7.4 Ensure all data in BigQuery has been classified", "run_status": 8, "run_error": "relation \"gcp_project\" does not exist (SQLSTATE 42P01)"}]}, {"group_id": "gcp_compliance.benchmark.cis_v300_8", "title": "8 Dataproc", "description": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_section_id": "8", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Dataproc", "type": "Benchmark"}, "summary": {"status": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}}, "groups": [], "controls": [{"summary": {"alarm": 0, "ok": 0, "info": 0, "skip": 0, "error": 1}, "results": null, "control_id": "control.cis_v300_8_1", "description": "When you use Dataproc, cluster and job data is stored on Persistent Disks (PDs) associated with the Compute Engine VMs in your cluster and in a Cloud Storage staging bucket. This PD and bucket data is encrypted using a Google-generated data encryption key (DEK) and key encryption key (KEK). The CMEK feature allows you to create, use, and revoke the key encryption key (KEK). Google still controls the data encryption key (DEK).", "severity": "", "tags": {"benchmark": "cis", "category": "Compliance", "cis_item_id": "8.1", "cis_level": "2", "cis_section_id": "8", "cis_type": "automated", "cis_version": "v3.0.0", "plugin": "gcp", "service": "GCP/Dataproc"}, "title": "8.1 Ensure that Dataproc Cluster is encrypted using CustomerManaged Encryption Key", "run_status": 8, "run_error": "relation \"gcp_bigquery_dataset\" does not exist (SQLSTATE 42P01)"}]}], "controls": null}], "controls": null}, "command": "cis_v300", "error": "Warning: Steampipe backend 'postgresql://steampipe@localhost:9193/steampipe' does not provide a plugin which satisfies requirement 'gcp@0.49.0' - required by 'gcp_compliance'\n"}}