from flask import Flask, request, jsonify
from flask_cors import CORS
import subprocess
import shlex
import logging
import sys
import json
import os
import threading
import time
from datetime import datetime
import google.auth
from google.oauth2 import service_account
from google.cloud import resourcemanager_v3
from google.cloud import asset_v1  # For project listing
from google.api_core.exceptions import GoogleAPICallError, PermissionDenied, NotFound
from google.protobuf.json_format import MessageToDict  # Add this line
from google.cloud import resourcemanager_v3
from generate_control import run_prompt
import re
import random
from google.cloud import secretmanager 
from google.cloud import secretmanager  # Add this import
from gcp.auth import auth, get_service_credentials
from google.auth import default
import run_steampipe
import signal
import atexit
from typing import List, Dict
import requests
sys.path.append('/app/shared')
from job_manager import job_manager
from steampipe_health import ensure_steampipe_running, initialize_steampipe_on_startup
import steampipe_health
from functools import wraps
import traceback

# Configure structured logging for Cloud Run
class StructuredMessage:
    def __init__(self, message, **kwargs):
        self.message = message
        self.kwargs = kwargs

    def __str__(self):
        return json.dumps({
            'message': self.message,
            'severity': 'INFO',
            'timestamp': datetime.utcnow().isoformat() + 'Z',
            **self.kwargs
        })

# Configure logging for Cloud Run
logging.basicConfig(
    level=logging.INFO,
    format='%(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

def log(message, **kwargs):
    """Enhanced logging format for Cloud Run"""
    logger.info(StructuredMessage(message, **kwargs))

# Constants
CONTROLS_FILE_PATH = './my_controls/my_controls.pp'  # Path to your .sp file
domain = os.getenv('DOMAIN')
NEOSEC_VERIFICATION_URL = f"https://{domain}/charge_credits"
NEOSEC_SUBSCRIPTION_URL = f"https://{domain}/check_subscription"

# Initialize global variables for process management
steampipe_process = None
max_retries = 3
database_timeout = 120  # seconds
AUTH_TYPE = os.getenv('AUTH_TYPE', 'adc')  # Default to 'adc' if not set

def cleanup_steampipe():
    """Stop Steampipe service and kill any lingering processes"""
    log("Performing Steampipe cleanup...")
    
    try:
        # First try force stop command which handles unknown states better
        try:
            subprocess.run(
                ["steampipe", "service", "stop", "--force"],
                capture_output=True,
                text=True,
                check=False,
                timeout=20  # Shorter timeout for force stop
            )
            log("Force stop command executed")
        except subprocess.TimeoutExpired:
            log("Force stop command timed out, proceeding with process kill", severity="WARNING")
        except Exception as e:
            log("Error during force stop", error=str(e), severity="ERROR")
        
        # Kill any leftover steampipe processes (more specific)
        subprocess.run(
            ["pkill", "-9", "-f", "steampipe"], 
            check=False, 
            capture_output=True
        )
        
        # Also kill postgres processes started by steampipe (more specific)
        subprocess.run(
            ["pkill", "-9", "-f", "postgres.*steampipe"], 
            check=False, 
            capture_output=True
        )
        
        time.sleep(3)  # Brief pause after cleanup
        log("Cleanup finished")
        
    except Exception as e:
        log("Error during cleanup", error=str(e), severity="ERROR")


def init_steampipe_service():
    """Initialize Steampipe service using improved health module"""
    try:
        initialize_steampipe_on_startup()
        return True
    except Exception as e:
        log(f"Failed to initialize Steampipe service: {e}", severity="ERROR")
        return False

# Initialize the Flask app
app = Flask(__name__)
CORS(app)
app.register_blueprint(auth)

# Register cleanup handlers
atexit.register(cleanup_steampipe)
signal.signal(signal.SIGTERM, lambda signum, frame: sys.exit(0))  # Ensure atexit runs on SIGTERM

def get_gcp_organization_id():
    """Retrieve the GCP organization ID using default credentials"""
    try:
        # Get default credentials and project ID
        if not project_id:
            return None, "No project ID found in default credentials"

        # Initialize clients
        projects_client = resourcemanager_v3.ProjectsClient(credentials=credentials)
        
        # Get project details
        project_name = f"projects/{project_id}"
        project = projects_client.get_project(name=project_name)
        parent = project.parent

        # Traverse parent hierarchy to find organization
        while parent:
            if parent.startswith("organizations/"):
                return parent.split('/')[1], None
            elif parent.startswith("folders/"):
                folder_id = parent.split('/')[1]
                folders_client = resourcemanager_v3.FoldersClient(credentials=credentials)
                folder = folders_client.get_folder(name=f"folders/{folder_id}")
                parent = folder.parent
            else:
                return None, f"Unknown parent type: {parent}"

        return None, "No organization found in hierarchy"

    except PermissionDenied as e:
        return None, f"Permission denied: {e}"
    except NotFound as e:
        return None, f"Resource not found: {e}"
    except GoogleAPICallError as e:
        return None, f"API Error: {e}"
    except Exception as e:
        return None, f"Unexpected error: {str(e)}"

def get_gcp_organization_id_with_creds(credentials, project_id):
    """Retrieve the GCP organization ID using provided credentials"""
    try:
        if not project_id:
            return None, "No project ID provided"
        
        # Initialize clients with provided credentials
        projects_client = resourcemanager_v3.ProjectsClient(credentials=credentials)
        
        # Get project details
        project_name = f"projects/{project_id}"
        project = projects_client.get_project(name=project_name)
        parent = project.parent
        
        # Traverse parent hierarchy to find organization
        while parent:
            if parent.startswith("organizations/"):
                return parent.split('/')[1], None
            elif parent.startswith("folders/"):
                folder_id = parent.split('/')[1]
                folders_client = resourcemanager_v3.FoldersClient(credentials=credentials)
                folder = folders_client.get_folder(name=f"folders/{folder_id}")
                parent = folder.parent
            else:
                return None, f"Unknown parent type: {parent}"
        
        return None, "No organization found in hierarchy"
    
    except PermissionDenied as e:
        return None, f"Permission denied: {e}"
    except NotFound as e:
        return None, f"Resource not found: {e}"
    except GoogleAPICallError as e:
        return None, f"API Error: {e}"
    except Exception as e:
        return None, f"Unexpected error: {str(e)}"

def save_output_to_json(project_id, benchmark, result_data):
    """Save the command output and results to a JSON file"""
    try:
        # Create output directory if it doesn't exist
        output_dir = "/app/steampipe-mod-gcp-compliance/outputs"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{output_dir}/{project_id}_{benchmark}_{timestamp}.json"
        
        # Prepare the JSON data
        json_data = {
            "project_id": project_id,
            "benchmark": benchmark,
            "timestamp": timestamp,
            "results": result_data
        }
        
        # Write JSON to file
        with open(filename, 'w') as f:
            json.dump(json_data, f, indent=2)
            
        return filename
    except Exception as e:
        logger.error(f"Error saving output to JSON file: {str(e)}")
        return None

def run_powerpipe_command_async(job_id, command_name, project_id, customer_id=None):
    """Execute powerpipe benchmark command asynchronously"""
    try:
        # Ensure Steampipe is running (same lightweight approach as sync version)
        try:
            ensure_steampipe_running()
        except Exception as e:
            error_msg = f"Steampipe service error: {str(e)}"
            log(error_msg)
            job_manager.update_job_status(job_id, 'failed', error=error_msg)
            return
        # If customer_id is provided, reinitialize credentials and Steampipe config
        if customer_id:
            creds, actual_project_id = get_service_credentials(customer_id)
            if actual_project_id:
                # Only override project_id if it's not "all" or "gcp" (preserve multi-project selection)
                if project_id not in ["all", "gcp"]:
                    project_id = actual_project_id
                # Regenerate Steampipe configuration with customer credentials
                log(f"Regenerating Steampipe config for customer {customer_id}")
                try:
                    is_cloud_run = os.getenv('K_SERVICE') is not None
                    run_steampipe.generate_and_save_config(
                        credentials=creds,
                        is_cloud_run=is_cloud_run
                    )
                    log("Steampipe config regenerated successfully")
                except Exception as e:
                    error_msg = f"Failed to regenerate Steampipe config: {str(e)}"
                    log(error_msg)
                    job_manager.update_job_status(job_id, 'failed', error=error_msg)
                    return
        # Update job status to running
        job_manager.update_job_status(job_id, 'running', progress=10)
        # Construct the command based on whether we want to scan all projects or a specific one
        formatted_project_id = project_id.replace("-", "_")
        # Build base command
        base_command = [
            "powerpipe",
            "benchmark",
            "run",
            command_name,
            "--var",
            f"project_id={project_id}",
            "--export=json"
        ]
        # For customer-specific credentials, we use the default connection
        # Don't add search-path-prefix as it may not exist
        if not customer_id and project_id != "gcp" and project_id != "all":
            # Only add search-path-prefix for non-customer runs
            connection_name = f"gcp_{formatted_project_id}"
            base_command.extend(["--search-path-prefix", connection_name])
        log(f"Executing command: {' '.join(base_command)}")
        job_manager.update_job_status(job_id, 'running', progress=30)
        # Create a process with pipe to capture output
        process = subprocess.Popen(
            base_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            text=True,
            cwd="/app/steampipe-mod-gcp-compliance"
        )
        # Set a timeout based on benchmark type
        timeout_seconds = 900  # 15 minutes default
        if "nist_800_53" in command_name:
            timeout_seconds = 1200  # 20 minutes for NIST
        try:
            # Read output and error streams with timeout
            stdout, stderr = process.communicate(timeout=timeout_seconds)
            job_manager.update_job_status(job_id, 'running', progress=80)
        except subprocess.TimeoutExpired:
            process.kill()
            stdout, stderr = process.communicate()
            error_msg = f"Benchmark timed out after {timeout_seconds} seconds"
            log(error_msg)
            job_manager.update_job_status(job_id, 'failed', error=error_msg)
            return
        # Try to find and read the exported JSON file
        json_file_path = None
        json_data = None
        for line in stdout.splitlines():
            if "File exported to" in line:
                json_file_path = line.split()[-1].strip()
                break
        if json_file_path and os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r') as f:
                    json_data = json.load(f)
                # Save the result
                job_manager.save_result(job_id, 'gcp', command_name, project_id, json_data)
                log(f"Job {job_id} completed successfully")
            except Exception as e:
                error_msg = f"Error reading JSON output: {str(e)}"
                log(error_msg)
                job_manager.update_job_status(job_id, 'failed', error=error_msg)
        else:
            # If no JSON file, save the raw output
            result = {
                'status': 'completed' if process.returncode == 0 else 'error',
                'stdout': stdout,
                'stderr': stderr,
                'return_code': process.returncode
            }
            if process.returncode == 0:
                job_manager.save_result(job_id, 'gcp', command_name, project_id, result)
            else:
                job_manager.update_job_status(job_id, 'failed', error=stderr or stdout)
    except Exception as e:
        error_msg = f"Error executing benchmark: {str(e)}"
        log(error_msg)
        job_manager.update_job_status(job_id, 'failed', error=error_msg)

def run_powerpipe_command(command_name, project_id, customer_id=None):
    """Execute powerpipe benchmark command with project_id and return real-time output"""
    try:
        # Ensure Steampipe is running
        try:
            ensure_steampipe_running()
        except Exception as e:
            return {
                "status": "error",
                "error": f"Steampipe service error: {str(e)}",
                "command": command_name
            }
            
        # If customer_id is provided, reinitialize credentials
        if customer_id:
            creds, actual_project_id = get_service_credentials(customer_id)
            if actual_project_id:
                # Only override project_id if it's not "all" or "gcp" (preserve multi-project selection)
                if project_id not in ["all", "gcp"]:
                    project_id = actual_project_id
        # Construct the command based on whether we want to scan all projects or a specific one
        formatted_project_id = project_id.replace("-", "_")
        
        # Build base command
        base_command = [
            "powerpipe",
            "benchmark",
            "run",
            command_name,
            "--var",
            f"project_id={project_id}",
            "--export=json"
        ]
        
        # Only add search-path-prefix if we have a specific project connection
        if project_id != "gcp" and project_id != "all":
            # Check if specific connection exists
            connection_name = f"gcp_{formatted_project_id}"
            base_command.extend(["--search-path-prefix", connection_name])
        
        log(f"Executing command: {' '.join(base_command)}")
        
        # Create a process with pipe to capture output
        process = subprocess.Popen(
            base_command,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True,
            text=True,
            cwd="/app/steampipe-mod-gcp-compliance"
        )
        
        # Read output and error streams
        stdout, stderr = process.communicate()

        # Try to find and read the exported JSON file
        json_file_path = None
        json_data = None
        
        for line in stdout.splitlines():
            if "File exported to" in line:
                json_file_path = line.split()[-1].strip()
                break
                
        if json_file_path and os.path.exists(json_file_path):
            try:
                with open(json_file_path, 'r') as f:
                    json_data = json.load(f)
            except Exception as e:
                log(f"Error reading JSON file: {str(e)}")

        # Save consolidated results to our JSON file
        # Only include the actual benchmark results, not the raw stdout which may contain invalid characters
        result_data = {
            "benchmark_results": json_data,
            "command": command_name,
            "error": stderr if stderr else None
        }
        
        output_file = save_output_to_json(project_id, command_name, result_data)

        return {
            "status": "success" if process.returncode == 0 else "error",
            "output": stdout,
            "error": stderr if stderr else None,
            "command": command_name,
            "return_code": process.returncode,
            "output_file": output_file,
            "benchmark_results": json_data
        }
    except Exception as e:
        log(f"Error executing command: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "command": command_name
        }

def extract_steampipe_data(response_text: str, control_name_suggestion: str = "testcontrol") -> dict:
    """Extracts and formats the Steampipe control and query.
    Returns a dict: {'control_block': ..., 'query_block': ..., 'control_name': ...}
    Returns error dict if critical parts are missing.
    """
    try:
        control_block = ""
        sql_query_block = ""
        control_name = control_name_suggestion

        # Extract the entire control block
        control_match = re.search(r'control\s+"([^"]+)"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if control_match:
            control_name = control_match.group(1).strip()
            control_block = control_match.group(0).strip()

        # Extract the entire query block
        query_match = re.search(r'query\s+"[^"]+"\s*\{[^}]*\}', response_text, re.DOTALL | re.IGNORECASE)
        if not query_match:  # If not in query block, look for a plain SQL block
           query_match = re.search(r'`sql(.*?)`', response_text, re.DOTALL | re.IGNORECASE)
           if query_match: # found sql block only
                sql_query_content = query_match.group(1).strip()
                # create query block
                sql_query_block = f'''query "{control_name}" {{
  sql = <<-EOQ
{sql_query_content}
  EOQ
}}'''
        else:
            sql_query_block = query_match.group(0).strip()

        # Construct default control block, if control block missing, but, sql block available
        if not control_block and sql_query_block:
            control_block = f"""control "{control_name}" {{
  title       = "Generated Control for {control_name}"
  description = "Generated control based on user prompt."
  query       = query.{control_name}
  tags = merge(local.gcp_compliance_common_tags, {{
    service = "GCP/Generated"  // Modify as needed
  }})
}}"""

        if not control_block or not sql_query_block: #check if we have both blocks
            return {"error": "Could not find both control and query blocks."}

        return {
            "control_block": control_block,
            "query_block": sql_query_block,
            "control_name": control_name,
        }

    except Exception as e:
        return {"error": f"Error during extraction/formatting: {e}"}

def write_steampipe_control_file(filepath: str, extracted_data: dict) -> None:
    """
    Overwrites the Steampipe control file with the new content.
    """
    control_name = extracted_data.get('control_name', 'testcontrol')  # Fallback
    control_block = extracted_data.get('control_block')
    query_block = extracted_data.get('query_block')

    if not control_block or not query_block:
        raise ValueError("Control block and query block are required.")

    try:
        # Construct the complete file content
        new_file_content = f"""locals {{
  my_controls_common_tags = merge(local.gcp_compliance_common_tags, {{
    type = "Benchmark"
  }})
}}

benchmark "my_controls" {{
  title       = "All Controls"
  description = "This benchmark contains all controls grouped by service to help you detect resource configurations that do not meet best practices."
  children = [
    control.{control_name},
  ]
  tags = local.my_controls_common_tags
}}

{control_block}

{query_block}
"""
        # Write the new content to the file (overwrites existing content)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(new_file_content)

    except Exception as e:
        raise Exception(f"Error writing to Steampipe control file: {e}")

def check_subscription():
    """
    Decorator to verify active subscription status without charging credits.
    Similar to charge_credits but uses the check_subscription endpoint instead.
    """
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip subscription check for development and when IAP is not configured
            if AUTH_TYPE == 'adc' or os.getenv('SKIP_IAP_CHECK', 'true').lower() == 'true':
                log("Skipping subscription verification (development mode or IAP not configured)", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    # For now, allow access without IAP token
                    log("No IAP token provided, allowing access (IAP not enforced)", severity="INFO")
                    return f(*args, **kwargs)

                # Make request to check_subscription endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to check subscription", 
                    url=NEOSEC_SUBSCRIPTION_URL,  # You'll need to define this constant
                    severity="DEBUG")
                    
                # No need to include cost_credits for subscription check
                data = {}
                
                response = requests.post(
                    NEOSEC_SUBSCRIPTION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from subscription check", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Subscription verification failed", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "No active subscription found"
                    }), 402  # Payment Required

                return f(*args, **kwargs)
            except Exception as e:
                log("Subscription verification error", 
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                # For now, allow access on error
                log("Allowing access despite subscription check error", severity="INFO")
                return f(*args, **kwargs)
        return decorated_function
    return decorator


def charge_credits(cost_credits=1):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Skip IAP check for development and when IAP is not configured
            if AUTH_TYPE == 'adc' or os.getenv('SKIP_IAP_CHECK', 'true').lower() == 'true':
                log("Skipping IAP verification (development mode or IAP not configured)", 
                    auth_type=AUTH_TYPE,
                    severity="INFO")
                return f(*args, **kwargs)
            try:
                # Get IAP token from headers
                iap_token = request.headers.get('X-Original-Iap-Jwt')
                log("Processing request headers", 
                    headers=dict(request.headers),
                    severity="DEBUG")
                
                host = request.headers.get('Referer')
                if not iap_token:
                    # For now, allow access without IAP token
                    log("No IAP token provided, allowing access (IAP not enforced)", severity="INFO")
                    return f(*args, **kwargs)

                # Make request to charge_credit endpoint on neosec marketplace
                headers = {
                    "token": iap_token,
                    "Content-Type": "application/json",
                    "subdomain": host
                }
                
                log("Making request to Neosec marketplace", 
                    url=NEOSEC_VERIFICATION_URL,
                    credits=cost_credits,
                    severity="DEBUG")
                    
                data = {
                    "cost_credits": cost_credits
                }
                
                response = requests.post(
                    NEOSEC_VERIFICATION_URL,
                    headers=headers,
                    json=data
                )

                log("Response from charge credit", 
                    response_status=response.status_code,
                    response_body=response.text,
                    severity="DEBUG")
                
                if response.status_code != 200:
                    log("Failed to charge credit", 
                        status_code=response.status_code,
                        severity="ERROR")
                    return jsonify({
                        "status": "error",
                        "error": "Failed to charge credit"
                    }), 400

                return f(*args, **kwargs)
            except Exception as e:
                log("IAP verification error", 
                    error=str(e),
                    stack_trace=traceback.format_exc(),
                    severity="ERROR")
                # For now, allow access on error
                log("Allowing access despite IAP check error", severity="INFO")
                return f(*args, **kwargs)
        return decorated_function
    return decorator

@app.route('/api/gcp/generate', methods=['POST'])
def generate():
    """API endpoint to generate control"""
    try:
        data = request.get_json()
        user_query = data['prompt']  # Get the prompt from the request

        response_text = run_prompt(user_query)
        extracted_data = extract_steampipe_data(response_text)

        if "error" in extracted_data:
            return jsonify({'error': extracted_data["error"], 'response': response_text}), 500

        write_steampipe_control_file(CONTROLS_FILE_PATH, extracted_data)

        # Return the *combined*, formatted output to the user.
        formatted_output = f"{extracted_data['control_block']}\n\n{extracted_data['query_block']}"
        return jsonify({'response': formatted_output})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/gcp/run-benchmark', methods=['POST'])
@check_subscription()
def run_benchmark():
    """API endpoint to run powerpipe benchmark synchronously (legacy)"""
    try:
        log(f"Request headers: {request.headers}")
        
        # Get customer ID from header
        customer_id = request.headers.get('X-Customer-ID')
        
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400
            
        if 'project_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'project_id' parameter"
            }), 400
            
        benchmark = data['benchmark']
        project_id = data['project_id']
        
        log(f"Running benchmark: {benchmark} for project: {project_id}, customer: {customer_id}")
        
        # Run the powerpipe command with project_id and customer_id
        result = run_powerpipe_command(benchmark, project_id, customer_id)
        return jsonify(result)
        
    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/gcp/run-benchmark-async', methods=['POST'])
@check_subscription()
def run_benchmark_async():
    """API endpoint to run powerpipe benchmark asynchronously"""
    try:
        # Get customer ID from header
        customer_id = request.headers.get('X-Customer-ID')
        
        data = request.get_json()
        if not data or 'benchmark' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'benchmark' parameter"
            }), 400
            
        if 'project_id' not in data:
            return jsonify({
                "status": "error",
                "error": "Missing 'project_id' parameter"
            }), 400
            
        benchmark = data['benchmark']
        project_id = data['project_id']
        
        # Create a job
        job_id = job_manager.create_job('gcp', benchmark, project_id)
        
        # Run the benchmark in a background thread
        thread = threading.Thread(
            target=run_powerpipe_command_async,
            args=(job_id, benchmark, project_id, customer_id),
            daemon=True
        )
        thread.start()
        
        log(f"Started async benchmark job: {job_id}")
        
        return jsonify({
            "status": "success",
            "job_id": job_id,
            "message": "Benchmark job started",
            "check_status_url": f"/api/gcp/job/{job_id}/status",
            "get_result_url": f"/api/gcp/job/{job_id}/result"
        })
        
    except Exception as e:
        log(f"API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/gcp/job/<job_id>/status', methods=['GET'])
def get_job_status(job_id):
    """Get the status of a running job"""
    job_status = job_manager.get_job_status(job_id)
    
    if not job_status:
        return jsonify({
            "status": "error",
            "error": "Job not found"
        }), 404
    
    return jsonify(job_status)

@app.route('/api/gcp/job/<job_id>/result', methods=['GET'])
def get_job_result(job_id):
    """Get the result of a completed job"""
    job_status = job_manager.get_job_status(job_id)
    
    if not job_status:
        return jsonify({
            "status": "error",
            "error": "Job not found"
        }), 404
    
    if job_status['status'] != 'completed':
        return jsonify({
            "status": "error",
            "error": f"Job is {job_status['status']}, not completed",
            "job_status": job_status
        }), 400
    
    result = job_manager.get_result_by_job_id(job_id)
    if result:
        return jsonify(result)
    else:
        return jsonify({
            "status": "error",
            "error": "Result not found"
        }), 404

@app.route('/api/gcp/latest-result/<benchmark>/<project_id>', methods=['GET'])
def get_latest_result(benchmark, project_id):
    """Get the latest result for a specific benchmark and project"""
    result = job_manager.get_latest_result('gcp', benchmark, project_id)
    
    if result:
        return jsonify(result)
    else:
        return jsonify({
            "status": "error",
            "error": "No results found for this benchmark and project"
        }), 404

@app.route('/api/gcp/list-results', methods=['GET'])
def list_results():
    """List all available results"""
    results = job_manager.list_available_results('gcp')
    return jsonify({
        "status": "success",
        "results": results
    })

@app.route('/api/gcp/jobs', methods=['GET'])
def list_jobs():
    """List all jobs"""
    jobs = job_manager.get_all_jobs('gcp')
    return jsonify({
        "status": "success",
        "jobs": jobs
    })

# Add alias for async benchmark endpoint
@app.route('/api/gcp/benchmark', methods=['POST'])
@check_subscription()
def run_benchmark_alias():
    """Alias endpoint for run_benchmark_async - more RESTful naming"""
    return run_benchmark_async()

# Add reports endpoint to list results for a customer
@app.route('/api/gcp/reports', methods=['GET'])
def get_reports():
    """Get all benchmark reports for the current customer"""
    try:
        # Get customer ID from header
        customer_id = request.headers.get('X-Customer-ID')
        
        # Get all available results using the correct method
        results = job_manager.list_available_results('gcp')
        
        # Get project_id and benchmark filters from query params
        project_id = request.args.get('project_id')
        benchmark = request.args.get('benchmark')
        
        # Filter results
        if project_id:
            results = [r for r in results if r.get('project_id') == project_id]
        if benchmark:
            results = [r for r in results if r.get('benchmark') == benchmark]
        
        return jsonify({
            "status": "success",
            "reports": results
        })
    except Exception as e:
        log(f"Error getting reports: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

# Add endpoint to list saved report files
@app.route('/api/gcp/listReports/<project_id>', methods=['GET'])
def list_report_files(project_id):
    """List all saved report files for a specific project"""
    try:
        output_dir = "/app/steampipe-mod-gcp-compliance/outputs"
        reports = []
        
        if os.path.exists(output_dir):
            for filename in os.listdir(output_dir):
                if filename.startswith(f"{project_id}_") and filename.endswith('.json'):
                    file_path = os.path.join(output_dir, filename)
                    file_stats = os.stat(file_path)
                    
                    # Parse benchmark name from filename
                    parts = filename.replace('.json', '').split('_')
                    benchmark = '_'.join(parts[1:-2]) if len(parts) > 2 else 'unknown'
                    
                    reports.append({
                        "filename": filename,
                        "benchmark": benchmark,
                        "project_id": project_id,
                        "timestamp": parts[-2] + '_' + parts[-1] if len(parts) > 2 else 'unknown',
                        "size": file_stats.st_size,
                        "modified": datetime.fromtimestamp(file_stats.st_mtime).isoformat()
                    })
        
        return jsonify({
            "status": "success",
            "project_id": project_id,
            "reports": sorted(reports, key=lambda x: x['modified'], reverse=True)
        })
    except Exception as e:
        log(f"Error listing reports: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

@app.route('/api/gcp/testcontrol', methods=['POST'])
def testcontrol():
    """
    Runs a Powerpipe command.  Expects 'command_name' and 'project_id' in the request body.
    """
    data = request.get_json()
    if not data or 'project_id' not in data:
        project_id = 'gcp'
    else:
        project_id = data['project_id']

    try:
        output = run_powerpipe_command("my_controls", project_id)
        log(f"{output}")
        # Return the output as a JSON response, and set the content type to plain text.
        return jsonify({'response': output})
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    """Basic endpoint to verify API is running"""
    # Check if steampipe is truly running by attempting a simple query
    steampipe_status = "running"
    try:
        result = subprocess.run(
            ["steampipe", "query", "select 1"],
            capture_output=True,
            text=True,
            check=False,
            timeout=5
        )
        if result.returncode != 0:
            steampipe_status = "error"
    except Exception as e:
        log(f"Health check error: {str(e)}")
        steampipe_status = "error"
        
    return jsonify({
        "status": "running",
        "steampipe_status": steampipe_status
    })
    
@app.route('/api/gcp/get-org-id', methods=['GET'])
def get_organization_id():
    """API endpoint to fetch GCP organization ID"""
    try:
        # Get customer ID from header
        customer_id = request.headers.get('X-Customer-ID')
        if not customer_id:
            return jsonify({"status": "error", "error": "Missing X-Customer-ID header"}), 400
        
        # Get credentials for customer
        credentials, actual_project_id = get_service_credentials(customer_id)
        if not credentials:
            return jsonify({"status": "error", "error": f"Failed to get credentials for customer {customer_id}"}), 401
        
        # Use the credentials to get organization ID
        org_id, error = get_gcp_organization_id_with_creds(credentials, actual_project_id)
        if error:
            log(f"Organization ID fetch error: {error}")
            return jsonify({"status": "error", "error": error}), 500
            
        return jsonify({
            "status": "success",
            "organization_id": org_id
        })
    except Exception as e:
        log(f"Organization ID API error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500 
        
@app.route('/api/gcp/get-projects', methods=['POST'])
def get_projects():
    """API endpoint to fetch all projects under a GCP organization"""
    try:
        data = request.get_json()
        if not data or 'organization_id' not in data:
            return jsonify({"status": "error", "error": "Missing organization_id"}), 400
            
        organization_id = data['organization_id']
        log(f"Fetching projects for organization: {organization_id}")
        
        projects, error = get_projects_for_organization(organization_id)
        
        if error:
            return jsonify({"status": "error", "error": error}), 500
            
        return jsonify({
            "status": "success",
            "projects": projects
        })
        
    except Exception as e:
        log(f"Error in get-projects API: {str(e)}")
        return jsonify({"status": "error", "error": str(e)}), 500

def get_projects_for_organization(organization_id: str) -> (List[Dict], str):
    """List all projects in organization including nested folders"""
    try:
        client = resourcemanager_v3.ProjectsClient(credentials=credentials)
        folders_client = resourcemanager_v3.FoldersClient(credentials=credentials)
        
        all_projects = []
        seen_folders = set()

        def process_container(container: str):
            """Recursively process folders and projects in a container"""
            # Process projects (REMOVED STATE CHECK)
            try:
                for project in client.list_projects(
                    request={"parent": container, "show_deleted": False}
                ):
                    all_projects.append({  # <-- REMOVED ACTIVE PROJECT CHECK
                        "project_id": project.project_id,
                        "project_number": project.name.split('/')[-1],
                        "name": project.display_name,
                        "parent": project.parent
                    })
            except Exception as e:
                log(f"Error listing projects in {container}: {str(e)}")

            # Process folders
            try:
                for folder in folders_client.list_folders(
                    request={"parent": container}
                ):
                    if folder.name not in seen_folders:
                        seen_folders.add(folder.name)
                        process_container(folder.name)  # Recursive call
            except Exception as e:
                log(f"Error listing folders in {container}: {str(e)}")

        # Start with organization root
        process_container(f"organizations/{organization_id}")
        
        return all_projects, None

    except Exception as e:
        log(f"Failed to list projects: {str(e)}")
        return None, f"Project listing failed: {str(e)}"

@app.route('/api/gcp/test-auth', methods=['POST'])
def test_auth():
    """Test endpoint to verify authentication without subscription check"""
    try:
        # Get customer ID from header
        customer_id = request.headers.get('X-Customer-ID')
        
        log(f"Test auth endpoint - Customer ID: {customer_id}")
        log(f"AUTH_TYPE: {AUTH_TYPE}")
        
        # Test getting credentials
        if customer_id:
            creds, project_id = get_service_credentials(customer_id)
            if creds:
                return jsonify({
                    "status": "success",
                    "message": "Authentication successful",
                    "customer_id": customer_id,
                    "project_id": project_id,
                    "auth_type": AUTH_TYPE,
                    "credential_type": type(creds).__name__
                })
            else:
                return jsonify({
                    "status": "error",
                    "error": f"Authentication failed: {project_id}",
                    "customer_id": customer_id,
                    "auth_type": AUTH_TYPE
                }), 500
        else:
            return jsonify({
                "status": "error",
                "error": "Missing X-Customer-ID header"
            }), 400
            
    except Exception as e:
        log(f"Test auth error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e),
            "auth_type": AUTH_TYPE
        }), 500

@app.route('/api/gcp/test-benchmark', methods=['POST'])
def test_benchmark():
    """Test endpoint to run benchmark without subscription check"""
    try:
        # Get request data
        data = request.get_json()
        customer_id = request.headers.get('X-Customer-ID')
        
        if not data:
            return jsonify({"status": "error", "error": "No data provided"}), 400
        
        benchmark = data.get('benchmark', 'cis_v300')
        project_id = data.get('project_id')
        
        log(f"Test benchmark endpoint - Customer ID: {customer_id}, Benchmark: {benchmark}, Project: {project_id}")
        
        # Get credentials if customer_id provided
        if customer_id:
            creds, actual_project_id = get_service_credentials(customer_id)
            if not creds:
                return jsonify({
                    "status": "error",
                    "error": f"Authentication failed: {actual_project_id}"
                }), 500
            
            # Use the project_id from credentials if not provided
            if not project_id and actual_project_id:
                project_id = actual_project_id
        
        if not project_id:
            return jsonify({"status": "error", "error": "Project ID required"}), 400
        
        # Initialize steampipe
        init_steampipe_service()
        
        # Run benchmark command
        try:
            result = run_powerpipe_command(benchmark, project_id, customer_id)
            
            # Parse the result
            if isinstance(result, dict) and result.get('json_data'):
                return jsonify({
                    "status": "success",
                    "benchmark": benchmark,
                    "project_id": project_id,
                    "result": result['json_data']
                })
            else:
                return jsonify({
                    "status": "success",
                    "benchmark": benchmark,
                    "project_id": project_id,
                    "output": result
                })
                
        except Exception as e:
            log(f"Benchmark execution error: {str(e)}")
            return jsonify({
                "status": "error",
                "error": str(e)
            }), 500
            
    except Exception as e:
        log(f"Test benchmark error: {str(e)}")
        return jsonify({
            "status": "error",
            "error": str(e)
        }), 500

def main():
    """Main application entry point"""
    global credentials, project_id, steampipe_process, AUTH_TYPE
    
    log("Starting application initialization...")
    
    # Initialize credentials first
    log("Initializing credentials...")
    credentials, project_id = get_service_credentials()
    log(f"Credentials initialized. Project ID: {project_id}")

    # Optional: Get organization ID with timeout
    org_id = None
    try:
        log("Attempting to detect organization ID (5s timeout)...")
        from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
        
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(get_gcp_organization_id)
            try:
                org_id, error = future.result(timeout=5)  # 5 second timeout
                if org_id:
                    log(f"Using organization ID: {org_id}")
                elif error:
                    log(f"Could not determine organization ID: {error}")
            except FutureTimeoutError:
                log("Organization ID detection timed out after 5s, continuing without it")
                future.cancel()
    except Exception as e:
        log(f"Error getting organization ID: {str(e)}")

    # Generate Steampipe configuration with credentials
    log("Generating Steampipe configuration...")
    is_cloud_run = os.getenv('K_SERVICE') is not None
    config_path = run_steampipe.generate_and_save_config(
        credentials=credentials, 
        is_cloud_run=is_cloud_run,
        organization_id=org_id  # Pass org_id if available, or None
    )
    
    # Start Steampipe service with three attempts
    if not init_steampipe_service():
       log("CRITICAL: Failed to start Steampipe service after 3 attempts. Exiting.")
       sys.exit(1)
    
    # Start Flask application
    log("Starting Flask application on port 8080...")
    app.run(host='0.0.0.0', port=8080, debug=False)

if __name__ == '__main__':
    main()